<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center space-x-2 py-5">
      <img src="@/assets/icons/tests.png" class="object-cover h-8 w-8" alt="tests-icon" />
      <h3 class="text-2xl font-semibold uppercase">{{ header }}</h3>
    </div>

    <div class="flex justify-between items-center w-full py-2 mb-2">
      <div class="flex items-center space-x-2">
        <CoreActionButton text="Refresh" color="warning" :icon="ArrowPathIcon" :click="() => {
          refreshTests();
        }
          " :loading="loading" />
        <div class="relative z-10 w-40">
          <CoreDropdown :items="testStatuses" v-model="statusSelected" />
        </div>
        <div class="w-44">
          <TestsFilterWards :wards="$metadata.wards" :initialSelection="selectedWards"
            @update:items-selected="onWardsUpdate" />
        </div>
        <div class="pt-1">
          <div class="bg-gray-100 pl-2.5 rounded flex items-center text-zinc-500">
            <FunnelIcon class="w-5 h-5 mr-2" />
            Date Range
            <div class="w-72 ml-2">
              <datepicker required position="left" @cleared="cleared" placeholder="select start & end date"
                :range="true" format="dd/MM/yyyy" input-class-name="datepicker" v-model="dateRange"
                :maxDate="new Date()" />
            </div>
          </div>
        </div>
      </div>

      <CoreSearchBar @update="update" v-model:search="search" />
    </div>

    <div class="py-3 flex items-center space-x-3">
      <CoreLabel :value="filter" close color="green" v-for="(filter, index) in filters" :key="index"
        @update="removeFilter" />
    </div>

    <CoreDatatable :headers="headers" :data="data" :loading="loading" :searchField="searchField"
      :searchValue="searchValue" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions"
      @update="updateTests">
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center">
          <div class="w-full flex items-center justify-between space-x-2">
            <div v-if="showMore !== item.id" class="flex items-center space-x-2">
              <TestsViewDialog ref="testsDialog" @update="updateTests" @order="onNewOrder">
                <CoreActionButton color="primary" text="View" :icon="ArrowTopRightOnSquareIcon" :click="() => {
                  openTestsDialog(item);
                }
                  " />
              </TestsViewDialog>

              <TestsCollectDialog v-if="
                item.status.toLowerCase() == 'verified' &&
                item.test_type_name.toLowerCase() ==
                'cross-match' &&
                !item.post_crossmatch_process
              " :item="item" @update="updateTests" />

              <TestsOutcomeDialog v-if="
                item.status.toLowerCase() == 'verified' &&
                item.test_type_name.toLowerCase() ==
                'cross-match' &&
                item.post_crossmatch_process &&
                item.post_crossmatch_process?.transfusion_outcome === null
              " :item="item" @update="updateTests" />

              <CoreActionButton v-if="
                item.status.toLowerCase() === 'completed' &&
                usePermissions().can.edit('test_results') &&
                !authStore.isClinicalStaff
              " :click="() => {
                checkStatus({ status: 'completed' }, item);
              }
                " text="Edit" color="success" :icon="PencilSquareIcon" />

              <CoreActionButton :color="status.color" :text="status.text" :icon="status.icon" :click="() => {
                checkStatus(status, item);
              }
                " :key="index" v-for="(status, index) in statuses"
                v-show="shouldDisplayButton(item, status) && !authStore.isClinicalStaff" />

              <CoreActionButton color="success" text="Accept" :icon="HandThumbUpIcon"
                :click="() => changeOrderStatus('accepted', item.order_id)"
                v-if="hideAcceptButton(item) && !authStore.isClinicalStaff" />
            </div>

            <CoreActionButton :click="() => shouldDisplayMore(item.id)" color="" text=""
              :icon="moreActions == true ? ChevronLeftIcon : ChevronRightIcon"
              v-if="hideRejectActions(item) && !authStore.isClinicalStaff" />
          </div>

          <div v-if="hideRejectActions(item) && showMore == item.id && !authStore.isClinicalStaff"
            class="flex items-center space-x-2">
            <TestsRejectReasonDialog v-for="(rejects, index) in rejectStatuses(item.status)" :key="rejects.name"
              :item="item" :text="rejects.name" :icon="rejects.icon" :action="rejects.action" @update="updateTests" />
          </div>
        </div>
      </template>
    </CoreDatatable>

    <div id="tests-container"></div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useAuthStore } from "@/store/auth";
import { useNuxtApp } from "#app";
import { useCookie } from "#app";
import moment from "moment";
import Package from "@/package.json";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import {
  HandThumbDownIcon,
  ChevronRightIcon,
  CheckBadgeIcon,
  SquaresPlusIcon,
  PlayIcon,
  PencilSquareIcon,
  HandThumbUpIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  FunnelIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/vue/24/solid/index.js";

import type { ServerOptions } from "vue3-easy-data-table";
import type {
  Department,
  DropdownItem,
  Header,
  Order,
  Page,
  RejectStatus,
  Request,
  Statuses,
  Test,
  TestActionButton,
  TestIndicatorType,
  TestType,
} from "@/types";
import PrinterService from "@/services/printer_service";
import { useWardStore } from "~/store/ward";

definePageMeta({
  layout: "dashboard",
});

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const { $toast, $metadata } = useNuxtApp();

useHead({
  title: `${Package.name.toUpperCase()} - ${authStore.department} Tests`,
});

const moreActions = ref<boolean>(false);
const showMore = ref<number>(0);
const testsDialog = ref();
const dateRange = ref<[string, string]>(["", ""]);
const search = ref<string>("");
const header = ref<string>("Tests Lists");
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
]);

const { value: testDisplayNamePreference } = usePreference("test_name_display", "preferred_name");

const headers = ref<Header>([
  { text: "PATIENT NO", value: "client.id", sortable: true },
  { text: "PATIENT NAME", value: "name", sortable: true },
  { text: "ACCESSION NO", value: "accession_number", sortable: true },
  { text: "PANEL TYPE", value: String(testDisplayNamePreference.value) === "full_name" ? "test_panel_name" : "test_panel_preferred_name", sortable: true },
  { text: "TEST", value: String(testDisplayNamePreference.value) === "full_name" ? "test_type_name" : "test_type_preferred_name", sortable: true },
  { text: "LOCATION", value: "requesting_ward", sortable: true },
  { text: "ORDER STATUS", value: "order_status", sortable: true },
  { text: "STATUS", value: "status", sortable: true },
  { text: "DATE REGISTERED", value: "created_date", sortable: true },
  { text: "ACTIONS", value: "actions", sortable: false },
]);

const statuses = ref<TestActionButton[]>([
  {
    text: "Results",
    color: "primary",
    icon: SquaresPlusIcon,
    status: "completed",
    show: "started",
    permission: "enter_test_results",
  },
  {
    text: "Accept",
    color: "success",
    icon: CheckBadgeIcon,
    status: "pending",
    show: "not-collected",
    permission: "accept_test_specimen",
  },
  {
    text: "Start",
    color: "warning",
    icon: PlayIcon,
    status: "started",
    show: "pending",
    permission: "start_test",
  },
  {
    text: "Authorize",
    color: "success",
    status: "verified",
    icon: CheckBadgeIcon,
    show: "completed",
    permission: "verify_test_results",
  },
]);

const rejectStatuses = (status: Statuses): RejectStatus[] => {
  const rejectStatusObjects: RejectStatus[] = [
    {
      name: "Reject",
      icon: HandThumbDownIcon,
      action: "rejected",
      permission: "void_test",
    },
    {
      name: "Void",
      icon: HandThumbDownIcon,
      action: "voided",
      permission: "void_test",
    },
    {
      name: "Notdone",
      icon: HandThumbDownIcon,
      action: "not_done",
      permission: "void_test",
    },
  ];

  return status.toLowerCase() === "verified"
    ? rejectStatusObjects.filter(
      (status) => status.name.toLowerCase() === "void"
    )
    : rejectStatusObjects;
};

const data = ref<any[]>([]);
const alert = useAlert();
const statusSelected = ref<DropdownItem>({ name: "select status" });
const searchField = ref("accession_number");
const searchValue = ref<string>("");
const cookie = useCookie("token");
const loading = ref<boolean>(false);
const isMounted = ref<boolean>(true);
const serverItemsLength = ref<number>(0);
const selectedWards = ref<string[]>([]);
const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 20,
  sortBy: "name",
});
const testStatuses = ref<{ id: number; name: string }[]>([]);
const filters = ref<{ origin: string; value: string }[]>([]);

const shouldDisplayMore = (id: number): void => {
  if (showMore.value == id) {
    showMore.value = 0;
  } else {
    showMore.value = id;
  }
};

const searchNlims = async (value: string): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.nlimsTestSearch}?tracking_number=${value.replace(
      /\$$/,
      ""
    )}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error } = await fetchRequest(request);
  if (data.value) {
    showTestDetails(data.value)
      .then((value: string) => {
        const newQuery = {
          ...route.query,
          search: value,
        };
        router.replace({ query: newQuery });
        search.value = value;
        searchValue.value = value;
        init();
      })
      .catch((error) => {
        console.error("error routing to merged test: ", error);
      });
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    $toast.error(`${error.value.data.error}`);
    loading.value = false;
  }
};

const init = async (): Promise<void> => {
  loadStatuses();
  loading.value = true;
  const { page, rowsPerPage } = serverOptions.value;
  let department_id = getIdFromName(
    authStore.user.departments,
    authStore.department
  );

  let lab_location_id = getIdFromName(
    authStore.locations,
    authStore.selectedLocation
  );

  let testStatus =
    statusSelected.value.name === "select status"
      ? ""
      : statusSelected.value.name !== undefined && statusSelected.value.name;

  const startDate =
    dateRange.value[0].toString() !== ""
      ? moment(dateRange.value[0].toString()).format("YYYY-MM-DD")
      : "";
  const endDate =
    dateRange.value[1].toString() !== ""
      ? moment(dateRange.value[1].toString()).format("YYYY-MM-DD")
      : "";

  const request: Request = {
    route: `${endpoints.tests
      }?minimal=true&page=${page}&per_page=${rowsPerPage}&status=${testStatus
        .toString()
        .toLowerCase()}&search=${search.value
      }&department_id=${department_id}&lab_location=${lab_location_id}&start_date=${startDate}&end_date=${endDate}&facility_sections=${selectedWards.value
      }`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data: responseData, error, pending } = await fetchRequest(request);
  loading.value = pending;

  if (responseData.value) {
    if (responseData.value.data.length == 0) {
      $toast.warning("No tests found within the specified period");
    }
    data.value = responseData.value.data.map((test: any) => ({
      ...test,
      name: `${capitalize(
        `${test.client?.first_name} ${test.client?.middle_name !== null ? test.client.middle_name : ""
        } ${test.client?.last_name}`
      )} (${test.client.sex}, ${calculateAge(test.client?.date_of_birth)} yrs)`,
      order_status: `${test.order_status !== null
        ? (
          test.order_status.charAt(0).toUpperCase() +
          test.order_status.slice(1)
        )
          .split("-")
          .join(" ")
        : ""
        }`,
      status: `${test.status !== null
        ? test.status.charAt(0).toUpperCase() + test.status.slice(1)
        : ""
        }`,
      created_date: moment(test.created_date)
        .utcOffset(2)
        .format(DATE_FORMAT_TIME),
    }));

    serverItemsLength.value = responseData.value.meta.total_count;
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value.data);
    $toast.error(ERROR_MESSAGE);
    loading.value = false;
  }
};

const loadStatuses = async (): Promise<void> => {
  const request: Request = {
    route: `${endpoints.testStatus}/all`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error }: any = await fetchRequest(request);

  if (data.value) {
    testStatuses.value = data.value.map(
      (status: { id: number; name: string }) => ({
        id: status.id,
        name: status.name.charAt(0).toUpperCase() + status.name.slice(1),
      })
    );
  }

  if (error.value) {
    console.error(error.value);
  }
};

const changeStatus = async (status: string, id: number): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.testStatus}/${id}/${status}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {},
  };

  const { data, error, pending }: any = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    init();
    loading.value = false;
  }

  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const onNewOrder = async (order: Order): Promise<void> => {
  if (!isEmpty(order)) {
    $toast.success(
      `Order with accession number ${order.accession_number} has been created successfully!`
    );
    if (
      await alert.alertConfirmation({
        message: "Do you want to print specimen label?",
      })
    ) {
      await PrinterService.printSpecimenLabel(order.accession_number);
    }
  }
  init();
};

const changeOrderStatus = async (status: string, id: number): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.orderStatus}/${status}`,
    method: "PUT",
    token: `${cookie.value}`,
    body: {
      order_id: id,
    },
  };

  const { data, error, pending }: any = await fetchRequest(request);
  loading.value = pending;
  if (data.value) {
    init();
    loading.value = false;
  }
  if (error.value) {
    console.error(error.value);
    loading.value = false;
  }
};

const checkStatus = (status: { status: string }, item: Test): void => {
  const statusActions = {
    completed: () => {
      const routeMap = {
        default: `/tests/result?accession_number=${item.accession_number}&test_id=${item.id}`,
        culture: `/tests/result/culture-sensitivity?accession_number=${item.accession_number}&test_id=${item.id}`,
      };

      const routeKey = item.test_type_name.toLowerCase().includes("culture")
        ? "culture"
        : "default";
      redirect(item, routeMap[routeKey]);
    },
    verified: () => openTestsDialog(item),
    default: () => {
      if (status.status !== "") {
        changeStatus(status.status, item.id);
      }
    },
  };
  const key = status.status as keyof typeof statusActions;
  const action = statusActions[key] || statusActions.default;
  action();
};

const getIdFromName = (
  departments: Array<Department>,
  name: string
): number | null | undefined => {
  const department = departments.find((dept) => dept.name === name);
  return department ? department.id : null;
};

const updateTests = (value: any): void => {
  showMore.value = 0;
  if (typeof value === "object") {
    serverOptions.value = value;
    const newQuery = {
      ...route.query,
      page: value.page,
      rowsPerPage: value.rowsPerPage,
    };
    router.replace({ query: newQuery });
  }
  init();
  moreActions.value = false;
};

const refreshTests = (): void => {
  searchValue.value = "";
  search.value = "";
  init();
};

const update = (value: string) => {
  search.value = value;
  searchValue.value = value;
  const newQuery = {
    ...route.query,
    search: value,
  };
  router.replace({ query: newQuery });
  if (value !== "") {
    if (checkTrackingNumber(value)) searchNlims(value);
    else updateTests(value);
  } else {
    init();
  }
};

const onWardsUpdate = (value: string[]): void => {
  if (value) {
    selectedWards.value = value;
    for (let i = filters.value.length - 1; i >= 0; i--) {
      const filter = filters.value[i];
      if (filter.origin === "wards" && !value.includes(filter.value)) {
        filters.value.splice(i, 1);
      }
    }
    value.forEach((ward: string) => {
      if (
        !filters.value.some(
          (filter) => filter.origin === "wards" && filter.value === ward
        )
      ) {
        filters.value.push({
          origin: "wards",
          value: ward,
        });
      }
    });
    init();
  }
};

const redirect = (item: Object, path: string): void => {
  router.push(path);
};

const checkTrackingNumber = (trackingNumber: string): boolean => {
  const regex = /^(x|l).*\d/;
  return regex.test(trackingNumber.toLowerCase());
};

const updateOnStatus = (value: { name: string }): void => {
  if (value.name !== "select status") {
    const foundStatusFilter = filters.value.find(
      (filter) => filter.origin === "statuses"
    );
    if (foundStatusFilter) {
      foundStatusFilter.value = value.name;
    } else {
      filters.value.push({
        origin: "statuses",
        value: value.name,
      });
    }
  }
  statusSelected.value = value;
  const newQuery = {
    ...route.query,
    status:
      value.name.toLowerCase() == "select status"
        ? ""
        : value.name.toLowerCase(),
  };
  router.replace({ query: newQuery });
  if (newQuery.status !== "") {
    init();
  }
};

const removeFilter = (value: { origin: string; value: string }): void => {
  if (value.origin === "statuses") {
    if (statusSelected.value.name !== "select status") {
      updateOnStatus({ name: "select status" });
    }
  }
  if (value.origin === "wards") {
    const wardIndex = selectedWards.value.indexOf(value.value);
    if (wardIndex !== -1) {
      selectedWards.value.splice(wardIndex, 1);
    }
  }
  const index = filters.value.findIndex(
    (filter) => filter.origin === value.origin
  );
  if (index !== -1) {
    filters.value.splice(index, 1);
  }
  init();
};

const isCompletedByCurrentUserOrSuperAdmin = (item: {
  status?: string;
  completed_by?: any;
}): boolean => {
  const currentUser = authStore.user;
  const completedBy = item.completed_by;

  if (completedBy) {
    if (completedBy.id !== currentUser.id) {
      return true;
    } else if (completedBy.is_super_admin === true) {
      return true;
    }
  }

  return false;
};

const shouldDisplayButton = (item: Test, status: TestActionButton): boolean => {
  const { hasPermission } = usePermissions();

  const lowerCaseStatus = item.status.toLowerCase();
  const lowerCaseShow = status.show.toLowerCase();

  if (lowerCaseStatus === lowerCaseShow) {
    const isCompletedByCurrentUserOrAdmin =
      isCompletedByCurrentUserOrSuperAdmin(item);
    if (
      lowerCaseStatus === "pending" &&
      item.order_status.toLowerCase() === "specimen not collected"
    ) {
      return false;
    }
    if (status.permission) {
      return (
        isCompletedByCurrentUserOrAdmin &&
        hasPermission.value(String(status.permission))
      );
    }
    return isCompletedByCurrentUserOrAdmin;
  }
  return false;
};

const hideAcceptButton = (item: any): boolean => {
  const testStatuses = new Set(["rejected", "not-done", "voided", "test-rejected"]);
  const specimenStatuses = new Set(["specimen not collected"]);
  return (
    !testStatuses.has(item.status.toLowerCase()) &&
    specimenStatuses.has(item.order_status.toLowerCase())
  );
};

const hideRejectActions = (item: { status: string }): boolean => {
  const { can } = usePermissions();
  const statuses: Set<string> = new Set([
    "voided",
    "test-rejected",
    "rejected",
    "not-done",
  ]);
  return !statuses.has(item.status.toLowerCase()) && can.void("test");
};

const cleared = (): void => {
  dateRange.value = ["", ""];
};

const openTestsDialog = (item: Object): void => {
  if (testsDialog !== null) {
    testsDialog.value.init(item);
  }
};

watch(
  () => authStore.department,
  (newDepartment, oldDepartment) => {
    if (newDepartment !== oldDepartment) {
      init();
    }
  }
);

watch(
  () => authStore.selectedLocation,
  (newSelectedLocation, oldSelectedLocation) => {
    if (newSelectedLocation !== oldSelectedLocation) {
      init();
    }
  }
);

watch(
  dateRange,
  (newValue, oldValue) => {
    if (isMounted.value) {
      isMounted.value = false;
      return;
    }
    if (newValue !== oldValue) {
      const startDate =
        newValue[0].toString() !== ""
          ? moment(newValue[0].toString()).format("YYYY-MM-DD")
          : "";
      const endDate =
        newValue[1].toString() !== ""
          ? moment(newValue[1].toString()).format("YYYY-MM-DD")
          : "";
      const newQuery = {
        ...route.query,
        from: startDate,
        to: endDate,
      };
      router.replace({ query: newQuery });
      init();
    }
  },
  { deep: true }
);

watch(
  statusSelected,
  (newValue) => {
    updateOnStatus(newValue);
  },
  { deep: true }
);

onMounted(() => {
  serverOptions.value = {
    page: Number(route.query.page ? route.query.page : 1),
    rowsPerPage: Number(route.query.rowsPerPage ? route.query.rowsPerPage : 20),
  };
  statusSelected.value = route.query.status
    ? { name: route.query.status.toString() }
    : { name: "select status" };
  search.value = route.query.search?.toString()
    ? route.query.search?.toString()
    : "";
  dateRange.value = [
    `${route.query.from?.toString() ? route.query.from?.toString() : ""}`,
    `${route.query.to?.toString() ? route.query.to?.toString() : ""}`,
  ];
  init();
});

onMounted(() => {
  const { selectedWard } = useWardStore();
  if (selectedWard) {
    // Add ward to selected wards array
    selectedWards.value = [selectedWard];

    // Add ward to filters display
    filters.value.push({
      origin: "wards",
      value: selectedWard
    });

    // Reload data with ward filter applied
    init();
  }
})
</script>
<style scoped></style>
